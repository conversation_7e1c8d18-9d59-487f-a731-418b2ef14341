#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import hashlib
from datetime import datetime
import logging

import peewee
from werkzeug.security import generate_password_hash, check_password_hash

from api.db import UserTenantRole
from api.db.db_models import DB, UserTenant
from api.db.db_models import User, Tenant
from api.db.services.common_service import CommonService
from api.utils import get_uuid, current_timestamp, datetime_format
from api.db import StatusEnum
from rag.settings import MINIO


class UserService(CommonService):
    """Service class for managing user-related database operations.

    This class extends CommonService to provide specialized functionality for user management,
    including authentication, user creation, updates, and deletions.

    Attributes:
        model: The User model class for database operations.
    """
    model = User

    @classmethod
    @DB.connection_context()
    def query(cls, cols=None, reverse=None, order_by=None, **kwargs):
        if 'access_token' in kwargs:
            access_token = kwargs['access_token']

            # Reject empty, None, or whitespace-only access tokens
            if not access_token or not str(access_token).strip():
                logging.warning("UserService.query: Rejecting empty access_token query")
                return cls.model.select().where(cls.model.id == "INVALID_EMPTY_TOKEN")  # Returns empty result

            # Reject tokens that are too short (should be UUID, 32+ chars)
            if len(str(access_token).strip()) < 32:
                logging.warning(f"UserService.query: Rejecting short access_token query: {len(str(access_token))} chars")
                return cls.model.select().where(cls.model.id == "INVALID_SHORT_TOKEN")  # Returns empty result

            # Reject tokens that start with "INVALID_" (from logout)
            if str(access_token).startswith("INVALID_"):
                logging.warning("UserService.query: Rejecting invalidated access_token")
                return cls.model.select().where(cls.model.id == "INVALID_LOGOUT_TOKEN")  # Returns empty result

            # Check token expiration
            from datetime import datetime
            result = super().query(cols=cols, reverse=reverse, order_by=order_by, **kwargs)
            if result:
                user = result.first()
                if user and user.token_expire_time and user.token_expire_time < datetime.now():
                    logging.warning(f"UserService.query: Token expired for user {user.id}")
                    return cls.model.select().where(cls.model.id == "INVALID_EXPIRED_TOKEN")  # Returns empty result

        # Call parent query method for valid requests
        return super().query(cols=cols, reverse=reverse, order_by=order_by, **kwargs)

    @classmethod
    @DB.connection_context()
    def filter_by_id(cls, user_id):
        """Retrieve a user by their ID.

        Args:
            user_id: The unique identifier of the user.

        Returns:
            User object if found, None otherwise.
        """
        try:
            user = cls.model.select().where(cls.model.id == user_id).get()
            return user
        except peewee.DoesNotExist:
            return None

    @classmethod
    @DB.connection_context()
    def query_user(cls, email, password):
        """Authenticate a user with email and password.

        Args:
            email: User's email address.
            password: User's password in plain text.

        Returns:
            User object if authentication successful, None otherwise.
        """
        user = cls.model.select().where((cls.model.email == email),
                                        (cls.model.status == StatusEnum.VALID.value)).first()
        if user and check_password_hash(str(user.password), password):
            return user
        else:
            return None

    @classmethod
    @DB.connection_context()
    def query_user_by_phone(cls, phone, password):
        """Authenticate a user with phone and password.

        Args:
            phone: User's phone number.
            password: User's password in plain text.

        Returns:
            User object if authentication successful, None otherwise.
        """
        user = cls.model.select().where((cls.model.phone == phone),
                                        (cls.model.status == StatusEnum.VALID.value)).first()
        if user and user.password and check_password_hash(str(user.password), password):
            return user
        else:
            return None

    @classmethod
    @DB.connection_context()
    def save(cls, **kwargs):
        if "id" not in kwargs:
            kwargs["id"] = get_uuid()
        if "password" in kwargs:
            kwargs["password"] = generate_password_hash(
                str(kwargs["password"]))

        kwargs["create_time"] = current_timestamp()
        kwargs["create_date"] = datetime_format(datetime.now())
        kwargs["update_time"] = current_timestamp()
        kwargs["update_date"] = datetime_format(datetime.now())
        obj = cls.model(**kwargs).save(force_insert=True)
        return obj

    @classmethod
    @DB.connection_context()
    def delete_user(cls, user_ids, update_user_dict):
        with DB.atomic():
            cls.model.update({"status": 0}).where(
                cls.model.id.in_(user_ids)).execute()

    @classmethod
    @DB.connection_context()
    def update_user(cls, user_id, user_dict):
        with DB.atomic():
            if user_dict:
                user_dict["update_time"] = current_timestamp()
                user_dict["update_date"] = datetime_format(datetime.now())
                cls.model.update(user_dict).where(
                    cls.model.id == user_id).execute()

    @classmethod
    @DB.connection_context()
    def get_all_users(cls, page=1, page_size=10):
        """获取所有用户列表（分页）"""
        offset = (page - 1) * page_size
        users = list(cls.model.select().where(
            cls.model.status == StatusEnum.VALID.value
        ).order_by(cls.model.create_time.desc()).offset(offset).limit(page_size).dicts())

        total = cls.model.select().where(
            cls.model.status == StatusEnum.VALID.value
        ).count()

        return users, total

    @classmethod
    @DB.connection_context()
    def create_user_by_admin(cls, nickname, phone, is_superuser=False):
        """管理员创建用户"""
        user_id = get_uuid()
        user_data = {
            "id": user_id,
            "nickname": nickname,
            "phone": phone,
            "email": f"{phone}@temp.local",  # 临时邮箱，因为email字段不能为空
            "is_superuser": is_superuser,
            "status": StatusEnum.VALID.value,
            "create_time": current_timestamp(),
            "create_date": datetime_format(datetime.now()),
            "update_time": current_timestamp(),
            "update_date": datetime_format(datetime.now()),
            "language": "Chinese",
            "color_schema": "Bright",
            "timezone": "UTC+8\tAsia/Shanghai",
            "is_authenticated": "1",
            "is_active": "1",
            "is_anonymous": "0"
        }

        cls.model(**user_data).save(force_insert=True)
        return user_id

    @classmethod
    @DB.connection_context()
    def delete_user_by_admin(cls, user_id):
        """管理员删除用户（软删除，修改手机号以释放唯一约束）"""
        # 先获取用户信息
        user = cls.model.select().where(cls.model.id == user_id).first()
        if not user:
            return

        # 生成唯一的删除标记，使用用户ID确保唯一性
        deleted_phone = f"{user.phone}_deleted_{user_id}"

        cls.model.update({
            "status": StatusEnum.INVALID.value,
            "phone": deleted_phone,
            "update_time": current_timestamp(),
            "update_date": datetime_format(datetime.now())
        }).where(cls.model.id == user_id).execute()

    @classmethod
    @DB.connection_context()
    def is_admin(cls, user_id):
        """检查用户是否为管理员"""
        user = cls.model.select().where(
            (cls.model.id == user_id) &
            (cls.model.status == StatusEnum.VALID.value)
        ).first()
        return user and user.is_superuser

    @classmethod
    @DB.connection_context()
    def get_by_access_token(cls, access_token):
        """根据access_token获取用户"""
        try:
            user = cls.model.select().where(
                (cls.model.access_token == access_token) &
                (cls.model.status == StatusEnum.VALID.value)
            ).first()
            return user
        except Exception:
            return None

    @classmethod
    @DB.connection_context()
    def is_superuser(cls, user_id):
        """检查用户是否为超级管理员"""
        user = cls.model.select().where(
            (cls.model.id == user_id) &
            (cls.model.status == StatusEnum.VALID.value)
        ).first()
        return user and user.is_superuser

    @classmethod
    @DB.connection_context()
    def get_all_users_ordered(cls, page=1, page_size=10):
        """获取所有用户，按创建时间倒序排列"""
        offset = (page - 1) * page_size

        # 查询总数
        total = cls.model.select().where(
            cls.model.status == StatusEnum.VALID.value
        ).count()

        # 查询用户列表，按创建时间倒序
        users_query = cls.model.select().where(
            cls.model.status == StatusEnum.VALID.value
        ).order_by(cls.model.create_time.desc()).offset(offset).limit(page_size)

        users = []
        for user in users_query:
            users.append({
                "id": user.id,
                "nickname": user.nickname,
                "phone": user.phone,
                "create_time": user.create_time,
                "is_superuser": user.is_superuser,

            })

        return users, total

    @classmethod
    @DB.connection_context()
    def get_by_phone(cls, phone):
        """通过手机号获取用户"""
        return cls.model.select().where(
            (cls.model.phone == phone) &
            (cls.model.status == StatusEnum.VALID.value)
        ).first()

    @classmethod
    @DB.connection_context()
    def phone_exists(cls, phone):
        """检查手机号是否已存在（只检查有效用户）"""
        return cls.model.select().where(
            (cls.model.phone == phone) &
            (cls.model.status == StatusEnum.VALID.value)
        ).exists()


class TenantService(CommonService):
    """Service class for managing tenant-related database operations.

    This class extends CommonService to provide functionality for tenant management,
    including tenant information retrieval and credit management.

    Attributes:
        model: The Tenant model class for database operations.
    """
    model = Tenant

    @classmethod
    @DB.connection_context()
    def get_info_by(cls, user_id):
        fields = [
            cls.model.id.alias("tenant_id"),
            cls.model.name,
            cls.model.llm_id,
            cls.model.embd_id,
            cls.model.rerank_id,
            cls.model.asr_id,
            cls.model.img2txt_id,
            cls.model.tts_id,
            cls.model.parser_ids,
            UserTenant.role]
        return list(cls.model.select(*fields)
                    .join(UserTenant, on=((cls.model.id == UserTenant.tenant_id) & (UserTenant.user_id == user_id) & (UserTenant.status == StatusEnum.VALID.value) & (UserTenant.role == UserTenantRole.OWNER)))
                    .where(cls.model.status == StatusEnum.VALID.value).dicts())

    @classmethod
    @DB.connection_context()
    def get_joined_tenants_by_user_id(cls, user_id):
        fields = [
            cls.model.id.alias("tenant_id"),
            cls.model.name,
            cls.model.llm_id,
            cls.model.embd_id,
            cls.model.asr_id,
            cls.model.img2txt_id,
            UserTenant.role]
        return list(cls.model.select(*fields)
                    .join(UserTenant, on=((cls.model.id == UserTenant.tenant_id) & (UserTenant.user_id == user_id) & (UserTenant.status == StatusEnum.VALID.value) & (UserTenant.role == UserTenantRole.NORMAL)))
                    .where(cls.model.status == StatusEnum.VALID.value).dicts())

    @classmethod
    @DB.connection_context()
    def decrease(cls, user_id, num):
        num = cls.model.update(credit=cls.model.credit - num).where(
            cls.model.id == user_id).execute()
        if num == 0:
            raise LookupError("Tenant not found which is supposed to be there")

    @classmethod
    @DB.connection_context()
    def user_gateway(cls, tenant_id):
        hashobj = hashlib.sha256(tenant_id.encode("utf-8"))
        return int(hashobj.hexdigest(), 16)%len(MINIO)


class UserTenantService(CommonService):
    """Service class for managing user-tenant relationship operations.

    This class extends CommonService to handle the many-to-many relationship
    between users and tenants, managing user roles and tenant memberships.

    Attributes:
        model: The UserTenant model class for database operations.
    """
    model = UserTenant

    @classmethod
    @DB.connection_context()
    def filter_by_id(cls, user_tenant_id):
        try:
            user_tenant = cls.model.select().where((cls.model.id == user_tenant_id) & (cls.model.status == StatusEnum.VALID.value)).get()
            return user_tenant
        except peewee.DoesNotExist:
            return None

    @classmethod
    @DB.connection_context()
    def save(cls, **kwargs):
        if "id" not in kwargs:
            kwargs["id"] = get_uuid()
        obj = cls.model(**kwargs).save(force_insert=True)
        return obj

    @classmethod
    @DB.connection_context()
    def get_by_tenant_id(cls, tenant_id):
        fields = [
            cls.model.id,
            cls.model.user_id,
            cls.model.status,
            cls.model.role,
            User.nickname,
            User.email,
            User.avatar,
            User.is_authenticated,
            User.is_active,
            User.is_anonymous,
            User.status,
            User.update_date,
            User.is_superuser]
        return list(cls.model.select(*fields)
                    .join(User, on=((cls.model.user_id == User.id) & (cls.model.status == StatusEnum.VALID.value) & (cls.model.role != UserTenantRole.OWNER)))
                    .where(cls.model.tenant_id == tenant_id)
                    .dicts())

    @classmethod
    @DB.connection_context()
    def get_tenants_by_user_id(cls, user_id):
        fields = [
            cls.model.tenant_id,
            cls.model.role,
            User.nickname,
            User.email,
            User.avatar,
            User.update_date
        ]
        return list(cls.model.select(*fields)
                    .join(User, on=((cls.model.tenant_id == User.id) & (UserTenant.user_id == user_id) & (UserTenant.status == StatusEnum.VALID.value)))
                    .where(cls.model.status == StatusEnum.VALID.value).dicts())

    @classmethod
    @DB.connection_context()
    def get_num_members(cls, user_id: str):
        cnt_members = cls.model.select(peewee.fn.COUNT(cls.model.id)).where(cls.model.tenant_id == user_id).scalar()
        return cnt_members

    @classmethod
    @DB.connection_context()
    def filter_by_tenant_and_user_id(cls, tenant_id, user_id):
        try:
            user_tenant = cls.model.select().where(
                (cls.model.tenant_id == tenant_id) & (cls.model.status == StatusEnum.VALID.value) &
                (cls.model.user_id == user_id)
            ).first()
            return user_tenant
        except peewee.DoesNotExist:
            return None