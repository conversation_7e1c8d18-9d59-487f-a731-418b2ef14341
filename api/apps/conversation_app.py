#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import json
import re
import traceback
from copy import deepcopy

import trio
from flask import Response, request
from flask_login import current_user, login_required

from api import settings
from api.db import LLMType
from api.db.db_models import APIToken
from api.db.services.conversation_service import ConversationService, structure_answer
from api.db.services.dialog_service import DialogService, ask, chat
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.llm_service import LLMBundle, TenantService
from api.db.services.user_service import UserTenantService, UserService
from api.db.services.faq_service import FAQService
from api.utils.api_utils import get_data_error_result, get_json_result, server_error_response, validate_request
from api.utils.image_utils import compress_image, validate_image, image_to_base64, optimize_image_for_vision_model
from rag.utils.storage_factory import STORAGE_IMPL
from graphrag.general.mind_map_extractor import MindMapExtractor
from rag.app.tag import label_question


def parse_qwq_thinking(content):
    """
    解析QwQ模型的思考过程和最终回答
    RAGFlow系统会将QwQ的reasoning_content包装在<think></think>标签中

    Args:
        content (str): QwQ模型的完整回答（可能包含<think>标签）

    Returns:
        tuple: (thinking_content, final_answer)
    """
    if not content:
        return "", content

    # 检查是否包含<think>标签（RAGFlow对reasoning_content的包装）
    think_pattern = r'<think>(.*?)</think>'
    think_matches = re.findall(think_pattern, content, re.DOTALL)

    if think_matches:
        # 提取所有思考过程
        thinking_content = ''.join(think_matches).strip()

        # 移除所有<think></think>标签，剩下的就是最终回答
        final_answer = re.sub(think_pattern, '', content, flags=re.DOTALL).strip()

        # 清理可能的多余空行
        final_answer = re.sub(r'\n\s*\n', '\n\n', final_answer).strip()

        return thinking_content, final_answer

    # 如果没有<think>标签，可能是普通模式或者标签处理有问题
    # 尝试基于内容特征进行分离（备用方案）
    thinking_indicators = [
        '让我想想', '让我分析', '首先', '然后', '接下来',
        '我认为', '我觉得', '考虑到', '这个问题', '分析一下'
    ]

    has_thinking = any(indicator in content for indicator in thinking_indicators)

    if has_thinking and len(content) > 200:
        # 尝试找到思考过程的结束标志
        thinking_end_patterns = [
            r'综上所述[，。]',
            r'总结[一下]*[，：。]',
            r'因此[，。]',
            r'所以[，。]',
            r'答案是[：。]',
            r'结论是[：。]'
        ]

        for pattern in thinking_end_patterns:
            matches = list(re.finditer(pattern, content))
            if matches:
                # 取最后一个匹配作为分界点
                thinking_end_pos = matches[-1].end()
                thinking_part = content[:thinking_end_pos].strip()
                answer_part = content[thinking_end_pos:].strip()

                if len(answer_part) >= 20:
                    return thinking_part, answer_part
                break

    # 如果无法分离，返回空的思考过程和原始内容
    return "", content


@manager.route("/set", methods=["POST"])  # noqa: F821
@login_required
def set_conversation():
    req = request.json
    conv_id = req.get("conversation_id")
    is_new = req.get("is_new")
    name = req.get("name", "New conversation")
    req["user_id"] = current_user.id

    if len(name) > 255:
        name = name[0:255]

    del req["is_new"]
    if not is_new:
        del req["conversation_id"]
        try:
            if not ConversationService.update_by_id(conv_id, req):
                return get_data_error_result(message="Conversation not found!")
            e, conv = ConversationService.get_by_id(conv_id)
            if not e:
                return get_data_error_result(message="Fail to update a conversation!")
            conv = conv.to_dict()
            return get_json_result(data=conv)
        except Exception as e:
            return server_error_response(e)

    try:
        e, dia = DialogService.get_by_id(req["dialog_id"])
        if not e:
            return get_data_error_result(message="Dialog not found")
        conv = {"id": conv_id, "dialog_id": req["dialog_id"], "name": name, "message": [{"role": "assistant", "content": dia.prompt_config["prologue"]}],"user_id": current_user.id}
        ConversationService.save(**conv)
        return get_json_result(data=conv)
    except Exception as e:
        return server_error_response(e)


@manager.route("/get", methods=["GET"])  # noqa: F821
def get():
    """
    获取会话详情
    ---
    tags:
      - 4-对话模块
    summary: 获取指定会话的详细信息
    description: |
      获取指定会话ID的完整对话记录，包括所有消息和知识库引用信息。

      **权限要求**: 普通用户
    security:
      - ApiKeyAuth: []
    parameters:
      - in: query
        name: conversation_id
        required: true
        type: string
        description: 会话ID
    responses:
      200:
        description: 成功获取会话详情
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: object
              description: 会话详细信息
              properties:
                id:
                  type: string
                  description: 会话ID
                name:
                  type: string
                  description: 会话名称
                message:
                  type: array
                  description: 消息列表
                  items:
                    type: object
                    properties:
                      role:
                        type: string
                        enum: ["user", "assistant"]
                        description: 消息角色
                      content:
                        type: string
                        description: 消息内容
                      id:
                        type: string
                        description: 消息ID
                      created_at:
                        type: number
                        description: 创建时间戳
                reference:
                  type: array
                  description: 知识库引用信息
                  items:
                    type: object
                    properties:
                      chunks:
                        type: array
                        description: 知识库片段
                avatar:
                  type: string
                  description: 助手头像
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败
      404:
        description: 会话不存在
      500:
        description: 服务器错误
    """
    # Token验证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return get_json_result(
            data=False,
            message="缺少访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    token = auth_header
    # 如果包含Bearer前缀，移除它
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]

    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    conv_id = request.args["conversation_id"]
    try:
        e, conv = ConversationService.get_by_id(conv_id)
        if not e:
            return get_data_error_result(message="Conversation not found!")

        # 简化权限检查：只检查是否为默认助手的对话
        from api.db.init_data import get_default_dialog_id
        default_dialog_id = get_default_dialog_id()
        if conv.dialog_id != default_dialog_id:
            return get_json_result(data=False, message="Can only access conversations in default dialog.", code=settings.RetCode.OPERATING_ERROR)

        # 获取助手头像
        avatar = None
        dialog = DialogService.query(id=conv.dialog_id)
        if dialog and len(dialog) > 0:
            avatar = dialog[0].icon

        def get_value(d, k1, k2):
            return d.get(k1, d.get(k2))

        for ref in conv.reference:
            if isinstance(ref, list):
                continue
            ref["chunks"] = [
                {
                    "id": get_value(ck, "chunk_id", "id"),
                    "content": get_value(ck, "content", "content_with_weight"),
                    "document_id": get_value(ck, "doc_id", "document_id"),
                    "document_name": get_value(ck, "docnm_kwd", "document_name"),
                    "dataset_id": get_value(ck, "kb_id", "dataset_id"),
                    "image_id": get_value(ck, "image_id", "img_id"),
                    "positions": get_value(ck, "positions", "position_int"),
                    "doc_type": get_value(ck, "doc_type", "doc_type_kwd"),
                }
                for ck in ref.get("chunks", [])
            ]

        conv = conv.to_dict()
        conv["avatar"] = avatar
        return get_json_result(data=conv)
    except Exception as e:
        return server_error_response(e)


@manager.route("/getsse/<dialog_id>", methods=["GET"])  # type: ignore # noqa: F821
def getsse(dialog_id):
    token = request.headers.get("Authorization").split()
    if len(token) != 2:
        return get_data_error_result(message='Authorization is not valid!"')
    token = token[1]
    objs = APIToken.query(beta=token)
    if not objs:
        return get_data_error_result(message='Authentication error: API key is invalid!"')
    try:
        e, conv = DialogService.get_by_id(dialog_id)
        if not e:
            return get_data_error_result(message="Dialog not found!")
        conv = conv.to_dict()
        conv["avatar"] = conv["icon"]
        del conv["icon"]
        return get_json_result(data=conv)
    except Exception as e:
        return server_error_response(e)


@manager.route("/rm", methods=["POST"])  # noqa: F821
@login_required
def rm():
    conv_ids = request.json["conversation_ids"]
    try:
        for cid in conv_ids:
            exist, conv = ConversationService.get_by_id(cid)
            if not exist:
                return get_data_error_result(message="Conversation not found!")
            tenants = UserTenantService.query(user_id=current_user.id)
            for tenant in tenants:
                if DialogService.query(tenant_id=tenant.tenant_id, id=conv.dialog_id):
                    break
            else:
                return get_json_result(data=False, message="Only owner of conversation authorized for this operation.", code=settings.RetCode.OPERATING_ERROR)
            ConversationService.delete_by_id(cid)
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/list", methods=["GET"])  # noqa: F821
def list_conversations():
    """
    获取历史对话列表
    ---
    tags:
      - 4-对话模块
    summary: 获取用户的历史对话列表
    description: |
      获取用户在默认助手下的所有历史对话记录，按创建时间倒序排列。

      **权限要求**: 普通用户

      **使用场景**:
      - 查看历史对话记录
      - 恢复之前的对话
      - 管理对话历史
    parameters:
      - in: query
        name: page
        type: integer
        required: false
        default: 1
        description: 页码，从1开始
      - in: query
        name: page_size
        type: integer
        required: false
        default: 20
        description: 每页数量，最大100
      - in: header
        name: Authorization
        type: string
        required: true
        description: Bearer token
    responses:
      200:
        description: 成功返回对话列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            message:
              type: string
              example: "success"
            data:
              type: object
              properties:
                conversations:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 对话ID
                      name:
                        type: string
                        description: 对话名称
                      create_time:
                        type: string
                        description: 创建时间
                      update_time:
                        type: string
                        description: 更新时间
                      message_count:
                        type: integer
                        description: 消息数量
                      last_message:
                        type: string
                        description: 最后一条消息内容
                total:
                  type: integer
                  description: 总数量
                page:
                  type: integer
                  description: 当前页码
                page_size:
                  type: integer
                  description: 每页数量
      401:
        description: 未授权
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性（普通用户即可使用）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要登录",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    from api.db.services.user_service import UserService

    # 提取token（支持两种格式：直接token或Bearer token）
    token = auth_header
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]  # 移除"Bearer "前缀
    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))

        # 限制每页最大数量
        if page_size > 100:
            page_size = 100
        if page < 1:
            page = 1

        # 简化权限检查：直接返回用户的对话列表
        # 查询用户的所有对话（不限制助手）
        convs = ConversationService.query(
            order_by=ConversationService.model.create_time,
            reverse=True,
            offset=(page - 1) * page_size,
            limit=page_size
        )

        # 获取总数
        total_convs = ConversationService.query()
        total = len(total_convs)

        # 转换为字典并添加额外信息
        conversations = []
        for conv in convs:
            conv_dict = conv.to_dict()

            # 添加消息数量
            conv_dict["message_count"] = len(conv_dict.get("message", []))

            # 添加最后一条消息内容（截取前100字符）
            messages = conv_dict.get("message", [])
            if messages and len(messages) > 0:
                last_msg = messages[-1].get("content", "")
                conv_dict["last_message"] = last_msg[:100] + "..." if len(last_msg) > 100 else last_msg
            else:
                conv_dict["last_message"] = ""

            conversations.append(conv_dict)

        return get_json_result(data={
            "conversations": conversations,
            "total": total,
            "page": page,
            "page_size": page_size
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return server_error_response(e)


@manager.route("/completion", methods=["POST"])  # noqa: F821
def completion():
    """
    对话补全接口
    ---
    tags:
      - 4-对话模块
    summary: 进行AI对话补全
    description: |
      与AI助手进行对话，支持流式和非流式返回。使用默认的"经略销售助手"进行对话。

      **权限要求**: 普通用户

      **使用场景**:
      - 与AI助手进行对话
      - 获取问题回答
      - 知识库检索问答
      - 多轮对话
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          required:
            - conversation_id
            - messages
          properties:
            conversation_id:
              type: string
              description: 对话ID，如果是新对话可以传空字符串
            messages:
              type: array
              description: 消息列表
              items:
                type: object
                properties:
                  role:
                    type: string
                    enum: [user, assistant, system]
                    description: 消息角色
                  content:
                    type: string
                    description: 消息内容
                  id:
                    type: string
                    description: 消息ID（可选）
            stream:
              type: boolean
              default: true
              description: 是否使用流式返回
            quote:
              type: boolean
              default: true
              description: 是否引用知识库内容
            doc_ids:
              type: array
              items:
                type: string
              description: 指定文档ID列表（可选）
            deep_thinking:
              type: boolean
              default: false
              description: 是否启用深度思考模式（使用支持深度思考的模型）
      - in: header
        name: Authorization
        type: string
        required: true
        description: Bearer token
    responses:
      200:
        description: 成功返回对话结果
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
            message:
              type: string
              example: "success"
            data:
              type: object
              properties:
                answer:
                  type: string
                  description: AI回答内容
                reference:
                  type: object
                  properties:
                    chunks:
                      type: array
                      description: 引用的知识库片段
                    doc_aggs:
                      type: array
                      description: 文档聚合信息
                conversation_id:
                  type: string
                  description: 对话ID
                message_id:
                  type: string
                  description: 消息ID
                deep_thinking:
                  type: boolean
                  description: 是否使用了深度思考模式
                model_used:
                  type: string
                  description: 实际使用的模型名称（深度思考模式时显示）
      401:
        description: 未授权
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性（普通用户即可使用）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要登录",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    from api.db.services.user_service import UserService

    # 提取token（支持两种格式：直接token或Bearer token）
    token = auth_header
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]  # 移除"Bearer "前缀
    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证请求参数
    req = request.json
    if not req:
        return get_data_error_result(message="请求体不能为空")

    if "messages" not in req:
        return get_data_error_result(message="messages参数是必需的")

    conversation_id = req.get("conversation_id")

    msg = []
    for m in req["messages"]:
        if m["role"] == "system":
            continue
        if m["role"] == "assistant" and not msg:
            continue
        msg.append(m)

    if not msg:
        return get_data_error_result(message="至少需要一条用户消息")

    message_id = msg[-1].get("id")

    try:
        # 获取或创建对话
        import logging
        logging.info(f"conversation_id: {conversation_id}")

        if conversation_id:
            e, conv = ConversationService.get_by_id(conversation_id)
            if not e:
                return get_data_error_result(message="对话不存在")
            logging.info(f"Found existing conversation: {type(conv)}")
        else:
            logging.info("Creating new conversation")
            # 创建新对话
            from api.db.init_data import get_default_dialog_id, create_default_dialog, get_default_knowledgebase_id, create_default_knowledgebase
            dialog_id = get_default_dialog_id()

            if not dialog_id:
                # 如果没有默认助手，尝试创建
                try:
                    # 先确保有默认知识库
                    kb_id = get_default_knowledgebase_id()
                    if not kb_id:
                        # 创建默认知识库
                        kb_id = create_default_knowledgebase(user.id, "qwen-embedding")

                    if kb_id:
                        # 创建默认助手
                        dialog_id = create_default_dialog(user.id, kb_id)

                    if not dialog_id:
                        return get_data_error_result(message="创建默认助手失败")
                except Exception as e:
                    return get_data_error_result(message=f"创建默认助手失败: {str(e)}")

            # 验证用户是否有权限访问助手（所有管理员都可以访问默认助手）
            dialog = DialogService.query(id=dialog_id)
            if not dialog:
                return get_data_error_result(message="默认助手不存在")

            # 检查是否为默认助手
            if dialog[0].name != "经略销售助手":
                # 如果不是默认助手，检查用户权限
                user_dialog = DialogService.query(tenant_id=user.id, id=dialog_id)
                if not user_dialog:
                    return get_data_error_result(message="无权访问助手")

            # 创建新对话
            from api.utils import get_uuid
            conversation_id = get_uuid()

            conv_data = {
                "id": conversation_id,
                "dialog_id": dialog_id,
                "name": "新对话",
                "message": []
            }
            saved_conv = ConversationService.save(**conv_data, tenant_id=user.id)
            if not saved_conv:
                return get_data_error_result(message="创建对话失败")

            # 调试日志
            import logging
            logging.info(f"Created conversation: {type(saved_conv)}, {saved_conv}")

            # 获取创建的对话对象
            e, conv = ConversationService.get_by_id(conversation_id)
            if not e:
                return get_data_error_result(message="获取创建的对话失败")

        conv.message = deepcopy(req["messages"])

        # 获取对话所属的助手
        e, dia = DialogService.get_by_id(conv.dialog_id)
        if not e:
            return get_data_error_result(message="助手不存在")

        # 验证用户权限（所有管理员都可以访问默认助手）
        if dia.name != "经略销售助手" and dia.tenant_id != user.id:
            return get_data_error_result(message="无权访问此助手")

        # 处理深度思考模式
        deep_thinking = req.get("deep_thinking", False)
        original_llm_id = dia.llm_id

        if deep_thinking:
            # 从配置文件读取深度思考模型
            from api import settings
            deep_thinking_config = settings.LLM.get("deep_thinking", {})

            if deep_thinking_config.get("enabled", False):
                default_model = deep_thinking_config.get("default_model", "qwq-plus")
                dia.llm_id = default_model

                # 对于需要enable_thinking参数的模型（如qwen-plus），添加参数
                if default_model.startswith('qwen-') and deep_thinking_config.get("enable_thinking_param", False):
                    # 为qwen系列模型添加enable_thinking参数
                    if not hasattr(dia, 'llm_setting') or not dia.llm_setting:
                        dia.llm_setting = {}
                    dia.llm_setting['enable_thinking'] = True

                # 修改系统提示以支持深度思考
                original_prompt_config = dia.prompt_config.copy()
                deep_thinking_prompt = """你是一个具备深度思考能力的AI助手。你可以：

1. 对复杂问题进行深入分析和推理
2. 展示你的思考过程，包括假设、推理步骤和结论
3. 基于知识库信息回答问题，但也可以进行逻辑推理和分析
4. 承认不确定性，并解释你的推理逻辑

请详细展示你的思考过程，包括：
- 问题分析
- 推理步骤
- 考虑的因素
- 得出的结论

如果有知识库信息可用，请结合使用：
{knowledge}

请始终保持逻辑清晰，推理严谨。"""

                dia.prompt_config["system"] = deep_thinking_prompt
                # 在深度思考模式下禁用empty_response，允许自由回答
                dia.prompt_config["empty_response"] = ""
                logging.info(f"启用深度思考模式，切换模型从 {original_llm_id} 到 {dia.llm_id}")
            else:
                logging.warning("深度思考模式未启用，继续使用原模型")
                deep_thinking = False  # 重置标志

        # 移除已处理的参数
        req_params = dict(req)
        if "conversation_id" in req_params:
            del req_params["conversation_id"]
        if "messages" in req_params:
            del req_params["messages"]
        if "stream" in req_params:
            del req_params["stream"]
        if "deep_thinking" in req_params:
            del req_params["deep_thinking"]

        if not conv.reference:
            conv.reference = []
        else:

            def get_value(d, k1, k2):
                return d.get(k1, d.get(k2))

            for ref in conv.reference:
                if isinstance(ref, list):
                    continue
                ref["chunks"] = [
                    {
                        "id": get_value(ck, "chunk_id", "id"),
                        "content": get_value(ck, "content", "content_with_weight"),
                        "document_id": get_value(ck, "doc_id", "document_id"),
                        "document_name": get_value(ck, "docnm_kwd", "document_name"),
                        "dataset_id": get_value(ck, "kb_id", "dataset_id"),
                        "image_id": get_value(ck, "image_id", "img_id"),
                        "positions": get_value(ck, "positions", "position_int"),
                        "doc_type": get_value(ck, "doc_type_kwd", "doc_type_kwd"),
                    }
                    for ck in ref.get("chunks", [])
                ]

        if not conv.reference:
            conv.reference = []
        conv.reference.append({"chunks": [], "doc_aggs": []})

        def stream():
            nonlocal dia, msg, req_params, conv, deep_thinking, original_llm_id
            original_prompt_config = None
            if deep_thinking:
                original_prompt_config = dia.prompt_config.copy()

            try:
                for ans in chat(dia, msg, True, **req_params):
                    ans = structure_answer(conv, ans, message_id, conv.id)
                    # 添加深度思考模式标识
                    if deep_thinking:
                        ans["deep_thinking"] = True
                        ans["model_used"] = dia.llm_id
                    yield "data:" + json.dumps({"code": 0, "message": "", "data": ans}, ensure_ascii=False) + "\n\n"
                ConversationService.update_by_id(conv.id, conv.to_dict())
            except Exception as e:
                traceback.print_exc()
                yield "data:" + json.dumps({"code": 500, "message": str(e), "data": {"answer": "**ERROR**: " + str(e), "reference": []}}, ensure_ascii=False) + "\n\n"
            finally:
                # 恢复原始模型和提示配置
                if deep_thinking:
                    dia.llm_id = original_llm_id
                    if original_prompt_config:
                        dia.prompt_config = original_prompt_config
            yield "data:" + json.dumps({"code": 0, "message": "", "data": True}, ensure_ascii=False) + "\n\n"

        if req.get("stream", True):
            resp = Response(stream(), mimetype="text/event-stream")
            resp.headers.add_header("Cache-control", "no-cache")
            resp.headers.add_header("Connection", "keep-alive")
            resp.headers.add_header("X-Accel-Buffering", "no")
            resp.headers.add_header("Content-Type", "text/event-stream; charset=utf-8")
            return resp

        else:
            answer = None
            original_prompt_config = None
            if deep_thinking:
                original_prompt_config = dia.prompt_config.copy()

            try:
                for ans in chat(dia, msg, False, **req_params):
                    answer = structure_answer(conv, ans, message_id, conv.id)
                    # 添加深度思考模式标识
                    if deep_thinking:
                        answer["deep_thinking"] = True
                        answer["model_used"] = dia.llm_id
                    ConversationService.update_by_id(conv.id, conv.to_dict())
                    break
            finally:
                # 恢复原始模型和提示配置
                if deep_thinking:
                    dia.llm_id = original_llm_id
                    if original_prompt_config:
                        dia.prompt_config = original_prompt_config
            return get_json_result(data=answer)
    except Exception as e:
        return server_error_response(e)


@manager.route("/tts", methods=["POST"])  # noqa: F821
@login_required
def tts():
    req = request.json
    text = req["text"]

    tenants = TenantService.get_info_by(current_user.id)
    if not tenants:
        return get_data_error_result(message="Tenant not found!")

    tts_id = tenants[0]["tts_id"]
    if not tts_id:
        return get_data_error_result(message="No default TTS model is set")

    tts_mdl = LLMBundle(tenants[0]["tenant_id"], LLMType.TTS, tts_id)

    def stream_audio():
        try:
            for txt in re.split(r"[，。/《》？；：！\n\r:;]+", text):
                for chunk in tts_mdl.tts(txt):
                    yield chunk
        except Exception as e:
            yield ("data:" + json.dumps({"code": 500, "message": str(e), "data": {"answer": "**ERROR**: " + str(e)}}, ensure_ascii=False)).encode("utf-8")

    resp = Response(stream_audio(), mimetype="audio/mpeg")
    resp.headers.add_header("Cache-Control", "no-cache")
    resp.headers.add_header("Connection", "keep-alive")
    resp.headers.add_header("X-Accel-Buffering", "no")

    return resp


@manager.route("/delete_msg", methods=["POST"])  # noqa: F821
@login_required
@validate_request("conversation_id", "message_id")
def delete_msg():
    req = request.json
    e, conv = ConversationService.get_by_id(req["conversation_id"])
    if not e:
        return get_data_error_result(message="Conversation not found!")

    conv = conv.to_dict()
    for i, msg in enumerate(conv["message"]):
        if req["message_id"] != msg.get("id", ""):
            continue
        assert conv["message"][i + 1]["id"] == req["message_id"]
        conv["message"].pop(i)
        conv["message"].pop(i)
        conv["reference"].pop(max(0, i // 2 - 1))
        break

    ConversationService.update_by_id(conv["id"], conv)
    return get_json_result(data=conv)


@manager.route("/thumbup", methods=["POST"])  # noqa: F821
@login_required
@validate_request("conversation_id", "message_id")
def thumbup():
    req = request.json
    e, conv = ConversationService.get_by_id(req["conversation_id"])
    if not e:
        return get_data_error_result(message="Conversation not found!")
    up_down = req.get("thumbup")
    feedback = req.get("feedback", "")
    conv = conv.to_dict()
    for i, msg in enumerate(conv["message"]):
        if req["message_id"] == msg.get("id", "") and msg.get("role", "") == "assistant":
            if up_down:
                msg["thumbup"] = True
                if "feedback" in msg:
                    del msg["feedback"]
            else:
                msg["thumbup"] = False
                if feedback:
                    msg["feedback"] = feedback
            break

    ConversationService.update_by_id(conv["id"], conv)
    return get_json_result(data=conv)


@manager.route("/ask", methods=["POST"])  # noqa: F821
@login_required
@validate_request("question", "kb_ids")
def ask_about():
    req = request.json
    uid = current_user.id

    def stream():
        nonlocal req, uid
        try:
            for ans in ask(req["question"], req["kb_ids"], uid):
                yield "data:" + json.dumps({"code": 0, "message": "", "data": ans}, ensure_ascii=False) + "\n\n"
        except Exception as e:
            yield "data:" + json.dumps({"code": 500, "message": str(e), "data": {"answer": "**ERROR**: " + str(e), "reference": []}}, ensure_ascii=False) + "\n\n"
        yield "data:" + json.dumps({"code": 0, "message": "", "data": True}, ensure_ascii=False) + "\n\n"

    resp = Response(stream(), mimetype="text/event-stream")
    resp.headers.add_header("Cache-control", "no-cache")
    resp.headers.add_header("Connection", "keep-alive")
    resp.headers.add_header("X-Accel-Buffering", "no")
    resp.headers.add_header("Content-Type", "text/event-stream; charset=utf-8")
    return resp


@manager.route("/mindmap", methods=["POST"])  # noqa: F821
@login_required
@validate_request("question", "kb_ids")
def mindmap():
    req = request.json
    kb_ids = req["kb_ids"]
    e, kb = KnowledgebaseService.get_by_id(kb_ids[0])
    if not e:
        return get_data_error_result(message="Knowledgebase not found!")

    embd_mdl = LLMBundle(kb.tenant_id, LLMType.EMBEDDING, llm_name=kb.embd_id)
    chat_mdl = LLMBundle(current_user.id, LLMType.CHAT)
    question = req["question"]
    ranks = settings.retrievaler.retrieval(question, embd_mdl, kb.tenant_id, kb_ids, 1, 12, 0.3, 0.3, aggs=False, rank_feature=label_question(question, [kb]))
    mindmap = MindMapExtractor(chat_mdl)
    mind_map = trio.run(mindmap, [c["content_with_weight"] for c in ranks["chunks"]])
    mind_map = mind_map.output
    if "error" in mind_map:
        return server_error_response(Exception(mind_map["error"]))
    return get_json_result(data=mind_map)


@manager.route("/related_questions", methods=["POST"])  # noqa: F821
@login_required
@validate_request("question")
def related_questions():
    req = request.json
    question = req["question"]
    chat_mdl = LLMBundle(current_user.id, LLMType.CHAT)
    prompt = """
Role: You are an AI language model assistant tasked with generating 5-10 related questions based on a user’s original query. These questions should help expand the search query scope and improve search relevance.

Instructions:
	Input: You are provided with a user’s question.
	Output: Generate 5-10 alternative questions that are related to the original user question. These alternatives should help retrieve a broader range of relevant documents from a vector database.
	Context: Focus on rephrasing the original question in different ways, making sure the alternative questions are diverse but still connected to the topic of the original query. Do not create overly obscure, irrelevant, or unrelated questions.
	Fallback: If you cannot generate any relevant alternatives, do not return any questions.
	Guidance:
	1. Each alternative should be unique but still relevant to the original query.
	2. Keep the phrasing clear, concise, and easy to understand.
	3. Avoid overly technical jargon or specialized terms unless directly relevant.
	4. Ensure that each question contributes towards improving search results by broadening the search angle, not narrowing it.

Example:
Original Question: What are the benefits of electric vehicles?

Alternative Questions:
	1. How do electric vehicles impact the environment?
	2. What are the advantages of owning an electric car?
	3. What is the cost-effectiveness of electric vehicles?
	4. How do electric vehicles compare to traditional cars in terms of fuel efficiency?
	5. What are the environmental benefits of switching to electric cars?
	6. How do electric vehicles help reduce carbon emissions?
	7. Why are electric vehicles becoming more popular?
	8. What are the long-term savings of using electric vehicles?
	9. How do electric vehicles contribute to sustainability?
	10. What are the key benefits of electric vehicles for consumers?

Reason:
	Rephrasing the original query into multiple alternative questions helps the user explore different aspects of their search topic, improving the quality of search results.
	These questions guide the search engine to provide a more comprehensive set of relevant documents.
"""
    ans = chat_mdl.chat(
        prompt,
        [
            {
                "role": "user",
                "content": f"""
Keywords: {question}
Related search terms:
    """,
            }
        ],
        {"temperature": 0.9},
    )
    return get_json_result(data=[re.sub(r"^[0-9]\. ", "", a) for a in ans.split("\n") if re.match(r"^[0-9]\. ", a)])


@manager.route("/chat_with_vision", methods=["POST"])  # noqa: F821
def chat_with_vision():
    """
    支持图片理解的对话接口
    ---
    tags:
      - 4-对话模块
    security:
      - ApiKeyAuth: []
    consumes:
      - multipart/form-data
    parameters:
      - in: formData
        name: question
        type: string
        required: true
        description: 用户问题
      - in: formData
        name: image
        type: file
        required: false
        description: 图片文件(可选)，支持jpg、png、gif等格式，最大200MB
      - in: formData
        name: use_knowledge_base
        type: boolean
        required: false
        default: true
        description: 是否使用知识库检索
      - in: formData
        name: stream
        type: boolean
        required: false
        default: true
        description: 是否流式返回
      - in: formData
        name: vision_model
        type: string
        required: false
        description: 指定视觉模型，如qwen-vl-plus、qwen-vl-max等
      - in: formData
        name: save_image
        type: boolean
        required: false
        default: false
        description: 是否保存图片到对象存储
    responses:
      200:
        description: 对话成功
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: object
              properties:
                content:
                  type: string
                  description: AI回答内容
                has_image:
                  type: boolean
                  description: 是否包含图片
                knowledge_base_used:
                  type: boolean
                  description: 是否使用了知识库
                image_info:
                  type: object
                  description: 图片处理信息
                  properties:
                    original_size:
                      type: integer
                      description: 原始文件大小
                    optimized_size:
                      type: integer
                      description: 优化后文件大小
                    filename:
                      type: string
                      description: 文件名
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败
      400:
        description: 请求参数错误
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性（普通用户即可使用）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要登录",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    from api.db.services.user_service import UserService

    # 提取token（支持两种格式：直接token或Bearer token）
    token = auth_header
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]  # 移除"Bearer "前缀
    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 使用默认助手
    from api.db.init_data import get_default_dialog_id, create_default_dialog, get_default_knowledgebase_id, create_default_knowledgebase
    dialog_id = get_default_dialog_id()

    if not dialog_id:
        # 如果没有默认助手，尝试创建
        try:
            # 先确保有默认知识库
            kb_id = get_default_knowledgebase_id()
            if not kb_id:
                # 创建默认知识库
                kb_id = create_default_knowledgebase(user.id, "qwen-embedding")

            if kb_id:
                # 创建默认助手
                dialog_id = create_default_dialog(user.id, kb_id)

            if not dialog_id:
                return get_data_error_result(message="创建默认助手失败")
        except Exception as e:
            return get_data_error_result(message=f"创建默认助手失败: {str(e)}")

    try:
        req = request.form.to_dict()
        question = req.get("question", "").strip()
        use_knowledge_base = req.get("use_knowledge_base", "true").lower() == "true"
        stream = req.get("stream", "true").lower() == "true"
        vision_model = req.get("vision_model", "")
        save_image = req.get("save_image", "false").lower() == "true"

        if not question:
            return get_data_error_result(message="问题不能为空")

        # 获取默认助手配置
        e, dialog = DialogService.get_by_id(dialog_id)
        if not e:
            return get_data_error_result(message="默认助手配置不存在")

        # 处理图片
        image_content = None
        image_info = None
        if "image" in request.files:
            image_file = request.files["image"]
            if image_file.filename:
                try:
                    # 读取图片数据
                    image_data = image_file.read()

                    # 验证图片
                    is_valid, error_msg = validate_image(image_data, max_size_mb=200)
                    if not is_valid:
                        return get_data_error_result(message=error_msg)

                    # 为视觉模型优化图片
                    optimized_image = optimize_image_for_vision_model(image_data, target_size_mb=10)

                    # 转换为base64
                    image_base64 = image_to_base64(optimized_image)
                    image_content = {
                        "type": "image_url",
                        "image_url": f"data:image/jpeg;base64,{image_base64}"
                    }

                    image_info = {
                        "original_size": len(image_data),
                        "optimized_size": len(optimized_image),
                        "filename": image_file.filename
                    }

                    # 如果需要保存图片到COS
                    if save_image:
                        try:
                            from api.db.init_data import get_default_knowledgebase_id
                            from api.utils import get_uuid

                            kb_id = get_default_knowledgebase_id()
                            if kb_id:
                                # 生成唯一的文件名
                                image_id = get_uuid()
                                image_location = f"chat_image_{image_id}.jpg"

                                # 存储优化后的图片到COS
                                STORAGE_IMPL.put(kb_id, image_location, optimized_image)

                                # 添加存储信息
                                image_info["stored"] = True
                                image_info["image_id"] = f"{kb_id}-{image_location}"
                                image_info["storage_location"] = image_location
                            else:
                                image_info["stored"] = False
                                image_info["storage_error"] = "Default knowledgebase not found"
                        except Exception as e:
                            image_info["stored"] = False
                            image_info["storage_error"] = str(e)
                    else:
                        image_info["stored"] = False

                except Exception as e:
                    return get_data_error_result(message=f"图片处理失败: {str(e)}")

        # 构建消息内容
        message_content = []
        tmp_path = None

        # 如果有图片，需要创建临时文件
        if image_content:
            import tempfile
            import os
            import base64
            from PIL import Image
            import io

            # 从base64重新获取图片数据
            image_base64 = image_content["image_url"].split(",")[1]
            image_data = base64.b64decode(image_base64)

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                tmp_path = tmp_file.name
                Image.open(io.BytesIO(image_data)).save(tmp_path)

            # 使用dashscope格式
            message_content.append({
                "type": "image",
                "image": f"file://{tmp_path}"
            })

        # 添加文本内容
        message_content.append({
            "type": "text",
            "text": question
        })

        # 获取模型
        try:
            if image_content:
                # 如果有图片，优先使用指定的视觉模型
                if vision_model:
                    chat_mdl = LLMBundle(dialog.tenant_id, LLMType.IMAGE2TEXT, vision_model)
                    if not chat_mdl:
                        chat_mdl = LLMBundle(dialog.tenant_id, LLMType.CHAT, vision_model)
                else:
                    # 尝试使用支持图像的Qwen模型
                    qwen_models = ["qwen-vl-plus", "qwen-vl-max", "qwen-vl-chat-v1"]
                    for model in qwen_models:
                        try:
                            chat_mdl = LLMBundle(dialog.tenant_id, LLMType.CHAT, model)
                            if chat_mdl:
                                break
                        except:
                            continue
            else:
                # 纯文本对话使用聊天模型
                chat_mdl = LLMBundle(dialog.tenant_id, LLMType.CHAT, dialog.llm_id)

            if not chat_mdl:
                return get_data_error_result(message="未找到可用的模型")

        except Exception as e:
            return get_data_error_result(message=f"模型初始化失败: {str(e)}")

        # 构建对话消息
        messages = [
            {
                "role": "user",
                "content": message_content if image_content else question
            }
        ]

        # 如果启用知识库检索
        knowledge_context = ""
        if use_knowledge_base and dialog.kb_ids:
            try:
                # 获取知识库
                kbs = KnowledgebaseService.get_by_ids(dialog.kb_ids)
                if kbs:
                    # 使用嵌入模型进行检索
                    embd_mdl = LLMBundle(dialog.tenant_id, LLMType.EMBEDDING, kbs[0].embd_id)
                    if embd_mdl:
                        # 简化的知识库检索
                        tenant_ids = [kb.tenant_id for kb in kbs]
                        kb_ids = [kb.id for kb in kbs]

                        # 执行检索
                        retrieval_result = settings.retrievaler.retrieval(
                            question, embd_mdl, tenant_ids, kb_ids,
                            1, 5, 0.1, 0.3, aggs=False
                        )

                        # 构建知识库上下文
                        if retrieval_result.get("chunks"):
                            knowledge_context = "\n".join([
                                chunk.get("content_with_weight", "")
                                for chunk in retrieval_result["chunks"][:3]
                            ])

                            # 添加知识库上下文到消息
                            if knowledge_context:
                                context_message = f"参考信息：\n{knowledge_context}\n\n基于以上信息回答问题："
                                if image_content:
                                    messages[0]["content"].insert(0, {
                                        "type": "text",
                                        "text": context_message
                                    })
                                else:
                                    messages[0]["content"] = context_message + question

            except Exception as e:
                # 知识库检索失败不影响对话
                pass

        # 创建对话记录
        conversation_id = None
        try:
            from api.utils import get_uuid
            conversation_id = get_uuid()

            # 构建对话消息记录
            conversation_messages = [
                {
                    "role": "user",
                    "content": question,
                    "id": get_uuid()
                }
            ]

            # 如果有图片，添加图片信息到消息中
            if image_info:
                conversation_messages[0]["image_info"] = image_info
                conversation_messages[0]["has_image"] = True

            # 创建对话记录
            conv_data = {
                "id": conversation_id,
                "dialog_id": dialog.id,
                "name": f"图片对话 - {question[:20]}..." if len(question) > 20 else f"图片对话 - {question}",
                "message": conversation_messages
            }

            ConversationService.save(**conv_data, tenant_id=user.id)

        except Exception as e:
            # 对话记录创建失败不影响对话执行
            pass

        # 执行对话
        try:
            if stream:
                # 流式返回
                def generate():
                    try:
                        for chunk in chat_mdl.chat_streamly(
                            "", messages, {"temperature": 0.7}
                        ):
                            if isinstance(chunk, str):
                                # 保持与completion接口一致的流式格式
                                ans_data = {
                                    "answer": chunk,
                                    "reference": {"chunks": [], "doc_aggs": []},
                                    "has_image": bool(image_content),
                                    "knowledge_base_used": bool(knowledge_context)
                                }
                                if image_info:
                                    ans_data["image_info"] = image_info
                                yield f"data: {json.dumps({'code': 0, 'message': '', 'data': ans_data}, ensure_ascii=False)}\n\n"
                            elif isinstance(chunk, (int, float)):
                                yield f"data: {json.dumps({'type': 'token_count', 'count': chunk}, ensure_ascii=False)}\n\n"
                        yield "data: [DONE]\n\n"
                    except Exception as e:
                        yield f"data: {json.dumps({'code': 500, 'message': str(e), 'data': False}, ensure_ascii=False)}\n\n"

                resp = Response(generate(), mimetype='text/event-stream')
                resp.headers.add_header("Cache-control", "no-cache")
                resp.headers.add_header("Connection", "keep-alive")
                resp.headers.add_header("X-Accel-Buffering", "no")
                resp.headers.add_header("Content-Type", "text/event-stream; charset=utf-8")
                return resp
            else:
                # 非流式返回
                response = chat_mdl.chat("", messages, {"temperature": 0.7})

                # 更新对话记录，添加AI回复
                if conversation_id:
                    try:
                        e, conv = ConversationService.get_by_id(conversation_id)
                        if e and conv:
                            # 添加AI回复到对话记录
                            ai_message = {
                                "role": "assistant",
                                "content": response,
                                "id": get_uuid()
                            }
                            conv.message.append(ai_message)
                            ConversationService.update_by_id(conversation_id, conv.to_dict())
                    except Exception as e:
                        # 更新失败不影响返回结果
                        pass

                # 保持与completion接口一致的返回格式
                result_data = {
                    "answer": response,
                    "reference": {
                        "chunks": [],
                        "doc_aggs": []
                    }
                }

                # 添加图片相关的额外信息
                if image_info:
                    result_data["image_info"] = image_info
                    result_data["has_image"] = True
                else:
                    result_data["has_image"] = False

                if knowledge_context:
                    result_data["knowledge_base_used"] = True
                else:
                    result_data["knowledge_base_used"] = False

                # 如果创建了对话，返回conversation_id
                if conversation_id:
                    result_data["conversation_id"] = conversation_id

                return get_json_result(data=result_data)

        except Exception as e:
            return get_data_error_result(message=f"对话生成失败: {str(e)}")

        finally:
            # 清理临时文件
            if tmp_path:
                try:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
                except:
                    pass

    except Exception as e:
        return server_error_response(e)


@manager.route("/quick_image_analysis", methods=["POST"])  # noqa: F821
def quick_image_analysis():
    """
    快速图片分析接口
    ---
    tags:
      - 4-对话模块
    security:
      - ApiKeyAuth: []
    consumes:
      - multipart/form-data
    parameters:
      - in: formData
        name: image
        type: file
        required: true
        description: 图片文件，支持jpg、png、gif等格式，最大200MB
      - in: formData
        name: prompt
        type: string
        required: false
        description: 自定义分析提示词
      - in: formData
        name: model
        type: string
        required: false
        default: qwen-vl-plus
        description: 指定视觉模型，如qwen-vl-plus、qwen-vl-max等
      - in: formData
        name: analysis_type
        type: string
        required: false
        default: general
        enum: [general, ocr, detailed]
        description: |
          分析类型：
          - general: 通用分析
          - ocr: OCR文字提取
          - detailed: 详细分析
    responses:
      200:
        description: 分析成功
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: object
              properties:
                analysis:
                  type: string
                  description: 图片分析结果
                model_used:
                  type: string
                  description: 使用的模型
                analysis_type:
                  type: string
                  description: 分析类型
                image_info:
                  type: object
                  description: 图片信息
                  properties:
                    filename:
                      type: string
                      description: 文件名
                    original_size:
                      type: integer
                      description: 原始文件大小
                    optimized_size:
                      type: integer
                      description: 优化后文件大小
                    compression_ratio:
                      type: string
                      description: 压缩比例
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败
      400:
        description: 请求参数错误
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性（普通用户即可使用）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要登录",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    from api.db.services.user_service import UserService
    import logging

    # 提取Bearer token
    if not auth_header.startswith('Bearer '):
        logging.warning(f"Invalid auth header format: {auth_header}")
        return get_json_result(
            data=False,
            message="Authorization格式错误",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    token = auth_header[7:]  # 移除"Bearer "前缀
    logging.info(f"Extracted token: {token}")

    user = UserService.get_by_access_token(token)
    logging.info(f"User found: {user is not None}")

    if not user or not user.access_token:
        logging.warning(f"Invalid token or user not found: token={token}")
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    try:
        if "image" not in request.files:
            return get_data_error_result(message="请上传图片文件")

        image_file = request.files["image"]
        if not image_file.filename:
            return get_data_error_result(message="请选择有效的图片文件")

        # 获取请求参数
        req = request.form.to_dict()
        custom_prompt = req.get("prompt", "").strip()
        model_name = req.get("model", "qwen-vl-plus")
        analysis_type = req.get("analysis_type", "general")

        try:
            # 读取图片数据
            image_data = image_file.read()

            # 验证图片
            is_valid, error_msg = validate_image(image_data, max_size_mb=200)
            if not is_valid:
                return get_data_error_result(message=error_msg)

            # 优化图片
            optimized_image = optimize_image_for_vision_model(image_data, target_size_mb=10)
            image_base64 = image_to_base64(optimized_image)

        except Exception as e:
            return get_data_error_result(message=f"图片处理失败: {str(e)}")

        # 构建分析提示
        if custom_prompt:
            prompt = custom_prompt
        else:
            if analysis_type == "ocr":
                prompt = "请提取图片中的所有文字内容，保持原有格式和布局。"
            elif analysis_type == "detailed":
                prompt = """请详细分析这张图片，包括：
1. 图片的主要内容和场景描述
2. 图片中的人物、物体、文字等具体元素
3. 图片的风格、色彩、构图特点
4. 如果有文字，请完整提取出来
5. 如果有数据、图表或表格，请分析其含义和数据
6. 其他值得注意的细节和信息
请用中文回答，内容要详细准确。"""
            else:  # general
                prompt = "请描述这张图片的主要内容，包括场景、人物、物体和重要信息。如果有文字请提取出来。"

        # 构建消息 - 使用Qwen dashscope库的格式（需要临时文件）
        import tempfile
        import os
        from PIL import Image
        import io

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            tmp_path = tmp_file.name
            # 将优化后的图片保存为临时文件
            Image.open(io.BytesIO(optimized_image)).save(tmp_path)

        try:
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "image": f"file://{tmp_path}"
                        },
                        {
                            "text": prompt
                        }
                    ]
                }
            ]

            # 使用HTTP API直接调用Qwen视觉模型
            try:
                import requests
                from api.utils import get_base_config

                # 获取Qwen配置
                qwen_config = get_base_config("user_default_llm", {})
                api_key = qwen_config.get("api_key", "")
                base_url = qwen_config.get("base_url", "")

                # 调试日志
                import logging
                logging.info(f"Qwen config: {qwen_config}")
                logging.info(f"API key: {api_key[:20] if api_key else 'None'}...")
                logging.info(f"Base URL: {base_url}")

                # 如果base_url为空，使用默认值
                if not base_url:
                    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
                    logging.info(f"Using default base URL: {base_url}")

                if not api_key:
                    return get_data_error_result(message="Qwen API密钥未配置")

                # 构建HTTP请求
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }

                data = {
                    "model": model_name,
                    "messages": [{
                        "role": "user",
                        "content": [{
                            "type": "image_url",
                            "image_url": f"data:image/jpeg;base64,{image_base64}"
                        }, {
                            "type": "text",
                            "text": prompt
                        }]
                    }],
                    "temperature": 0.3
                }

                # 发送请求
                url = f"{base_url}/chat/completions"
                response = requests.post(url, headers=headers, json=data, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    tokens = result.get('usage', {}).get('total_tokens', 0)

                    response_text = content
                else:
                    return get_data_error_result(message=f"API调用失败: {response.status_code} - {response.text}")

                return get_json_result(data={
                    "analysis": response_text,
                    "model_used": model_name,
                    "analysis_type": analysis_type,
                    "tokens_used": tokens,
                    "image_info": {
                        "filename": image_file.filename,
                        "original_size": len(image_data),
                        "optimized_size": len(optimized_image),
                        "compression_ratio": f"{len(optimized_image)/len(image_data)*100:.1f}%"
                    }
                })

            except Exception as e:
                return get_data_error_result(message=f"图片分析失败: {str(e)}")

        finally:
            # 清理临时文件
            try:
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
            except:
                pass

    except Exception as e:
        return server_error_response(e)


# FAQ相关接口
@manager.route('/faq/get/<faq_id>', methods=['GET'])  # noqa: F821
def get_faq(faq_id):
    """
    获取FAQ详情
    ---
    tags:
      - 4-对话模块
    summary: 获取FAQ详情
    description: |
      根据FAQ ID获取具体的问题和答案详情。

      **功能说明：**
      - 根据FAQ ID返回完整的问题和答案信息
      - 包含问题分类、频率、关键词等元数据
      - 用于用户点击FAQ问题后显示详细答案

      **使用场景：**
      - 用户点击FAQ问题查看详细答案
      - 客服获取标准回复内容
      - FAQ内容管理和编辑
    parameters:
      - name: faq_id
        in: path
        type: string
        required: true
        description: FAQ的唯一标识ID
        example: "1a8269926ca411f0aff1525400b50619"
    responses:
      200:
        description: 成功获取FAQ详情
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
              description: 响应码，0表示成功
            message:
              type: string
              example: "success"
              description: 响应消息
            data:
              type: object
              properties:
                id:
                  type: string
                  description: FAQ唯一标识
                  example: "1a8269926ca411f0aff1525400b50619"
                question:
                  type: string
                  description: 问题内容
                  example: "常规股权变更需要哪些资料"
                answer:
                  type: string
                  description: 答案内容
                  example: "1.新旧股东身份证正反面照片及联系方式，以及新股东股东各占比例。\n2.变更的内容:如法人，经营范围，地址，注册资金等相关内容。\n3.营业执照正副本原件及公章。\n4.法人注册的公司渝快办账号密码。\n5.股权转让时间上一月财务报表，并上传电子税务局系统。\n6.新旧股东个税账号和密码，以及法人电子税务局账号和密码。\n7.实缴凭证(如有实缴需提供验资报告或银行实缴凭证）。\n8.股权转让协议。\n9.股东会决议与股东决定。\n10.公司章程(需要调取档案)。\n11.委托书。"
                frequency:
                  type: string
                  description: 问题频率
                  example: "高频问题"
                category:
                  type: string
                  description: 问题分类
                  example: "股权变更"
                keywords:
                  type: string
                  description: 关键词，逗号分隔
                  example: "股权变更,资料,材料,需要什么,怎么办"
      404:
        description: FAQ不存在
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 102
            message:
              type: string
              example: "FAQ不存在"
            data:
              type: object
              example: null
      400:
        description: 请求参数错误
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 102
            message:
              type: string
              example: "FAQ ID格式错误"
            data:
              type: object
              example: null
      500:
        description: 服务器内部错误
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 100
            message:
              type: string
              example: "服务器内部错误"
            data:
              type: object
              example: null
    """
    try:
        if not faq_id or not faq_id.strip():
            return get_data_error_result(message="FAQ ID不能为空")

        e, faq = FAQService.get_by_id(faq_id)
        if not e:
            return get_data_error_result(message="FAQ不存在")

        return get_json_result(data={
            "id": faq.id,
            "question": faq.question,
            "answer": faq.answer,
            "frequency": faq.frequency,
            "category": faq.category,
            "keywords": faq.keywords
        })

    except Exception as e:
        return server_error_response(e)


@manager.route('/faq/list', methods=['GET'])  # noqa: F821
def list_faq():
    """
    获取FAQ常见问题列表
    ---
    tags:
      - 4-对话模块
    summary: 获取FAQ常见问题列表
    description: |
      获取系统中的常见问题列表，支持分页查询。

      **功能说明：**
      - 返回所有有效的FAQ问题和答案
      - 支持分页查询，默认每页20条
      - 按照排序顺序和创建时间排序
      - 包含问题分类和频率信息

      **使用场景：**
      - 用户查看常见问题
      - 客服快速回复常见咨询
      - 系统FAQ管理
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        minimum: 1
        description: 页码，从1开始
        example: 1
      - name: page_size
        in: query
        type: integer
        required: false
        default: 20
        minimum: 1
        maximum: 100
        description: 每页数量，最大100条
        example: 10
    responses:
      200:
        description: 成功获取FAQ列表
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 0
              description: 响应码，0表示成功
            message:
              type: string
              example: "success"
              description: 响应消息
            data:
              type: object
              properties:
                list:
                  type: array
                  description: FAQ列表
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: FAQ唯一标识
                        example: "1a8269926ca411f0aff1525400b50619"
                      question:
                        type: string
                        description: 问题内容
                        example: "常规股权变更需要哪些资料"
                      answer:
                        type: string
                        description: 答案内容
                        example: "1.新旧股东身份证正反面照片及联系方式..."
                      frequency:
                        type: string
                        description: 问题频率
                        example: "高频问题"
                      category:
                        type: string
                        description: 问题分类
                        example: "股权变更"
                      keywords:
                        type: string
                        description: 关键词，逗号分隔
                        example: "股权变更,资料,材料,需要什么,怎么办"
                      sort_order:
                        type: integer
                        description: 排序顺序
                        example: 1
                total:
                  type: integer
                  description: 总记录数
                  example: 15
                page:
                  type: integer
                  description: 当前页码
                  example: 1
                page_size:
                  type: integer
                  description: 每页数量
                  example: 10
                total_pages:
                  type: integer
                  description: 总页数
                  example: 2
      400:
        description: 请求参数错误
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 102
            message:
              type: string
              example: "参数错误"
            data:
              type: object
              example: null
      500:
        description: 服务器内部错误
        schema:
          type: object
          properties:
            code:
              type: integer
              example: 100
            message:
              type: string
              example: "服务器内部错误"
            data:
              type: object
              example: null
    """
    try:
        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 20))

        # 参数验证
        if page < 1:
            return get_data_error_result(message="页码必须大于0")
        if page_size < 1 or page_size > 100:
            return get_data_error_result(message="每页数量必须在1-100之间")

        # 获取FAQ列表
        faqs = FAQService.get_all_active(page, page_size)
        total = FAQService.count_active()

        # 转换为字典格式
        result = []
        for faq in faqs:
            result.append({
                "id": faq.id,
                "question": faq.question,
                "answer": faq.answer,
                "frequency": faq.frequency,
                "category": faq.category,
                "keywords": faq.keywords,
                "sort_order": faq.sort_order
            })

        return get_json_result(data={
            "list": result,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        })

    except ValueError:
        return get_data_error_result(message="页码和每页数量必须为有效数字")
    except Exception as e:
        return server_error_response(e)


@manager.route('/faq/create', methods=['POST'])  # noqa: F821
@login_required
@validate_request("question", "answer")
def create_faq():
    """创建FAQ"""
    try:
        req = request.json

        faq = FAQService.create_faq(
            question=req["question"],
            answer=req["answer"],
            frequency=req.get("frequency", "高频问题"),
            category=req.get("category"),
            keywords=req.get("keywords"),
            sort_order=req.get("sort_order", 0)
        )

        return get_json_result(data={
            "id": faq.id,
            "question": faq.question,
            "answer": faq.answer,
            "frequency": faq.frequency,
            "category": faq.category,
            "keywords": faq.keywords,
            "sort_order": faq.sort_order
        })

    except Exception as e:
        return server_error_response(e)


@manager.route('/faq/batch_create', methods=['POST'])  # noqa: F821
@login_required
@validate_request("faq_list")
def batch_create_faq():
    """批量创建FAQ"""
    try:
        req = request.json
        faq_list = req["faq_list"]

        created_faqs = FAQService.batch_create_faqs(faq_list)

        result = []
        for faq in created_faqs:
            result.append({
                "id": faq.id,
                "question": faq.question,
                "answer": faq.answer,
                "frequency": faq.frequency,
                "category": faq.category,
                "keywords": faq.keywords,
                "sort_order": faq.sort_order
            })

        return get_json_result(data={
            "created_count": len(result),
            "faqs": result
        })

    except Exception as e:
        return server_error_response(e)
